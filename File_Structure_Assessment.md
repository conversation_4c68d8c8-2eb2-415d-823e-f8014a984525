# St. Mary's File Structure Assessment Questions

## Current System Understanding

### PowerChurch Usage

1. Which specific PowerChurch modules are currently in use? (Membership, Contributions, Accounting, Events, etc.)
2. Who has access to which modules in PowerChurch?
3. What types of information do you currently store directly in PowerChurch vs. external files?
4. Are there any PowerChurch reports that you regularly export and save? Where do you store them?
5. What are the pain points with the current PowerChurch implementation?

### Current File Organization

1. Where are church files currently stored? (Local computers, shared drive, cloud storage, etc.)
2. Do you have any existing folder structure or naming conventions, formal or informal?
3. How do staff members currently find files they need?
4. Which staff members create and manage the most files?
5. Are there any existing backup procedures in place?
6. What permissions or access control systems are currently implemented?

### Pain Points & Challenges

1. What are your biggest frustrations with the current file organization?
2. Have there been instances where important files couldn't be located?
3. Do you experience issues with file version control? (Multiple versions, confusion over which is current)
4. How much time do staff members typically spend searching for files?
5. Are there duplicate files across different storage locations?
6. Do you have issues with inconsistent file naming?

## Operational Workflow

### Document Creation & Management

1. Which departments or individuals create the most documents?
2. What types of documents are created most frequently?
3. How are documents typically shared among staff?
4. Are certain files or documents accessed more frequently than others?
5. Do you have any recurring document types that are created on a schedule?

### Ministry-Specific Needs

1. What are all the active ministries at St. Mary's?
2. Which ministries generate the most documentation or files?
3. Are there seasonal ministries with specific file management needs?
4. Do ministry leaders have specific file organization requirements?
5. How do volunteers access needed files and documents?

### Communication & Collaboration

1. How do staff members collaborate on documents?
2. What is the review and approval process for official church documents?
3. How are files shared with church members, board members, or other stakeholders?
4. Do you use any collaboration tools besides PowerChurch?
5. How are meeting notes and decisions documented and stored?

## Technical Considerations

### Storage & Access

1. What is your current storage capacity across all systems?
2. Who needs remote access to files, and how is that currently handled?
3. What devices (computers, tablets, phones) are used to access church files?
4. Are there bandwidth or connectivity issues that affect file access?

### Security & Compliance

1. What types of sensitive information do you store? (financial, personal, pastoral counseling, etc.)
2. Are there specific security requirements for different types of files?
3. Do you have any compliance requirements related to document retention?
4. How long do you typically keep different types of documents?

## Future Needs & Growth

1. Are there any upcoming initiatives that will change your file management needs?
2. Do you anticipate growth in specific ministries that might generate more files?
3. Are there plans to implement new technologies that would affect file storage?
4. What would an ideal file organization system look like from your perspective?
5. What metrics would indicate success for a new file organization system?

## Implementation & Change Management

1. Who would be responsible for implementing a new file structure?
2. How much time can staff dedicate to reorganizing files?
3. What training would be needed for staff to adopt a new system?
4. What concerns do you have about transitioning to a new file organization system?
5. What has prevented better organization in the past?

## Timeline Justification Questions

1. Approximately how many existing files need to be organized?
2. How far back do your electronic records go?
3. Are there paper documents that need to be digitized as part of this process?
4. Would you prefer a gradual transition or a complete overhaul?
5. How many staff members will need training on the new system?
6. Are there any critical deadlines or events that could affect the implementation timeline?
