# Project Revision Log

This document tracks major updates, additions, and suggestions made during the development of the [ORGANIZATION_NAME] Data Management and File Organization Project.

---

## Revision 1: Initial Draft Build Out (June 15, 2025)

**Summary of Actions:**

* Generalized project content from "St. Mary's" to `[ORGANIZATION_NAME]`.
* Revised and updated `File_Naming_Convention.md` into a standalone SOP: `File_Naming_Convention_SOP.md`.
* Revised and updated `File_Structure_Proposal.md` into a standalone SOP: `File_Structure_and_Navigation_SOP.md`.
* Created a new standalone SOP: `Data_Retention_and_Archiving_SOP.md`, incorporating 1-year general retention for archiving and 5-year retention for tax-related documents.
* Provided conceptual guidance for adapting `main_interactive.htm` to a Vite-React structure and integrating generalized visuals.

**Suggestions for Improvement / Next Steps:**

* Develop the remaining core SOPs:
  * NAS Usage & Best Practices
  * PowerChurch Data Management & Export SOP
  * Data Backup & Recovery Procedure (for IT/Admin)
* Begin actual implementation of the Vite-React project, using the generated markdown content as data sources.
* Design and create the actual generalized visual assets (diagrams, flowcharts) to be integrated into the interactive project.
* Review and refine the language in all SOPs for clarity and conciseness, specifically for a non-technical audience where appropriate.
* Consider adding an "Introduction to the System" document or section that acts as a high-level overview for all users.

## Revision 2: NAS SOP and Project Purpose Document (June 16, 2025)

**Summary of Actions:**
* Created a new standalone SOP: `NAS_Usage_and_Best_Practices_SOP.md`, incorporating details about the QNAP TS-453A and its drives, and general NAS best practices.
* Created a `Project_Purpose_Document.md` that explains the project's goals, scope, and high-level design, drawing inspiration from System Design Document (SDD) principles.

**Suggestions for Improvement / Next Steps:**
* Develop the remaining core SOPs:
    * PowerChurch Data Management & Export SOP
    * Data Backup & Recovery Procedure (for IT/Admin)
* Further refine the `Project_Purpose_Document.md` by integrating more specific technical details if a full SDD is later required, potentially incorporating elements from the CMS and Wildart SDD templates once they can be fully reviewed.
* Continue with the development of the Vite-React interactive application, using the generated SOPs as content.
* Begin design and creation of generalized visual assets.